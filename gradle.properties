# Project-wide Gradle settings.
# Chinese mirrors already configured in settings.gradle/build.gradle

# Java toolchain
org.gradle.jvmargs=-Xmx2g -Dfile.encoding=UTF-8
# Prefer Android Studio bundled JDK 17 to avoid JDK 21 jlink issues
org.gradle.java.home=C:\\Program Files\\Android\\Android Studio\\jbr

# Android config
ANDROID_USE_ANDROIDX=true
android.useAndroidX=true
android.enableJetifier=true

# Application identifiers (change here to switch package)
APP_ID=com.augment.crazybrain
APP_NAME=疯狂大脑

MIN_SDK=24
TARGET_SDK=34
COMPILE_SDK=34

# Java compatibility
JAVA_VERSION=17

# Workaround AGP jlink-related failures
android.experimental.disableJdkImageTransform=true
android.experimental.javaCompilationWithJdkImage=false

