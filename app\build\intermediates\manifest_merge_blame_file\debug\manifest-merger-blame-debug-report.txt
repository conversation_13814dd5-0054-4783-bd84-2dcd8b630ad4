1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.augment.crazybrain.debug"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <permission
11-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\b6654cc2e9c5fe1c44db73258b312664\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
12        android:name="com.augment.crazybrain.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
12-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\b6654cc2e9c5fe1c44db73258b312664\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
13        android:protectionLevel="signature" />
13-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\b6654cc2e9c5fe1c44db73258b312664\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
14
15    <uses-permission android:name="com.augment.crazybrain.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
15-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\b6654cc2e9c5fe1c44db73258b312664\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
15-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\b6654cc2e9c5fe1c44db73258b312664\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
16
17    <application
17-->C:\Apps\CrazyBrainV1\app\src\main\AndroidManifest.xml:4:5-19:19
18        android:allowBackup="true"
18-->C:\Apps\CrazyBrainV1\app\src\main\AndroidManifest.xml:5:9-35
19        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
19-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\b6654cc2e9c5fe1c44db73258b312664\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
20        android:debuggable="true"
21        android:extractNativeLibs="false"
22        android:icon="@mipmap/ic_launcher"
22-->C:\Apps\CrazyBrainV1\app\src\main\AndroidManifest.xml:6:9-43
23        android:label="@string/app_name"
23-->C:\Apps\CrazyBrainV1\app\src\main\AndroidManifest.xml:7:9-41
24        android:roundIcon="@mipmap/ic_launcher_round"
24-->C:\Apps\CrazyBrainV1\app\src\main\AndroidManifest.xml:8:9-54
25        android:supportsRtl="true"
25-->C:\Apps\CrazyBrainV1\app\src\main\AndroidManifest.xml:9:9-35
26        android:theme="@style/Theme.CrazyBrain" >
26-->C:\Apps\CrazyBrainV1\app\src\main\AndroidManifest.xml:10:9-48
27        <activity
27-->C:\Apps\CrazyBrainV1\app\src\main\AndroidManifest.xml:11:9-18:20
28            android:name="com.augment.crazybrain.MainActivity"
28-->C:\Apps\CrazyBrainV1\app\src\main\AndroidManifest.xml:12:13-63
29            android:exported="true" >
29-->C:\Apps\CrazyBrainV1\app\src\main\AndroidManifest.xml:13:13-36
30            <intent-filter>
30-->C:\Apps\CrazyBrainV1\app\src\main\AndroidManifest.xml:14:13-17:29
31                <action android:name="android.intent.action.MAIN" />
31-->C:\Apps\CrazyBrainV1\app\src\main\AndroidManifest.xml:15:17-69
31-->C:\Apps\CrazyBrainV1\app\src\main\AndroidManifest.xml:15:25-66
32
33                <category android:name="android.intent.category.LAUNCHER" />
33-->C:\Apps\CrazyBrainV1\app\src\main\AndroidManifest.xml:16:17-77
33-->C:\Apps\CrazyBrainV1\app\src\main\AndroidManifest.xml:16:27-74
34            </intent-filter>
35        </activity>
36
37        <provider
37-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\54755d9f56bf932731ff4e33704107ca\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
38            android:name="androidx.startup.InitializationProvider"
38-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\54755d9f56bf932731ff4e33704107ca\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
39            android:authorities="com.augment.crazybrain.debug.androidx-startup"
39-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\54755d9f56bf932731ff4e33704107ca\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
40            android:exported="false" >
40-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\54755d9f56bf932731ff4e33704107ca\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
41            <meta-data
41-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\54755d9f56bf932731ff4e33704107ca\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
42                android:name="androidx.emoji2.text.EmojiCompatInitializer"
42-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\54755d9f56bf932731ff4e33704107ca\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
43                android:value="androidx.startup" />
43-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\54755d9f56bf932731ff4e33704107ca\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
44            <meta-data
44-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\d9b8f893883abb33984aef4c5c4491c9\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
45                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
45-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\d9b8f893883abb33984aef4c5c4491c9\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
46                android:value="androidx.startup" />
46-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\d9b8f893883abb33984aef4c5c4491c9\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
47            <meta-data
47-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\5a660b02a26ababb3b15cc631f7fb17f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
48                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
48-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\5a660b02a26ababb3b15cc631f7fb17f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
49                android:value="androidx.startup" />
49-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\5a660b02a26ababb3b15cc631f7fb17f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
50        </provider>
51
52        <receiver
52-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\5a660b02a26ababb3b15cc631f7fb17f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
53            android:name="androidx.profileinstaller.ProfileInstallReceiver"
53-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\5a660b02a26ababb3b15cc631f7fb17f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
54            android:directBootAware="false"
54-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\5a660b02a26ababb3b15cc631f7fb17f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
55            android:enabled="true"
55-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\5a660b02a26ababb3b15cc631f7fb17f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
56            android:exported="true"
56-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\5a660b02a26ababb3b15cc631f7fb17f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
57            android:permission="android.permission.DUMP" >
57-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\5a660b02a26ababb3b15cc631f7fb17f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
58            <intent-filter>
58-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\5a660b02a26ababb3b15cc631f7fb17f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
59                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
59-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\5a660b02a26ababb3b15cc631f7fb17f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
59-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\5a660b02a26ababb3b15cc631f7fb17f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
60            </intent-filter>
61            <intent-filter>
61-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\5a660b02a26ababb3b15cc631f7fb17f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
62                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
62-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\5a660b02a26ababb3b15cc631f7fb17f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
62-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\5a660b02a26ababb3b15cc631f7fb17f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
63            </intent-filter>
64            <intent-filter>
64-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\5a660b02a26ababb3b15cc631f7fb17f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
65                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
65-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\5a660b02a26ababb3b15cc631f7fb17f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
65-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\5a660b02a26ababb3b15cc631f7fb17f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
66            </intent-filter>
67            <intent-filter>
67-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\5a660b02a26ababb3b15cc631f7fb17f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
68                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
68-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\5a660b02a26ababb3b15cc631f7fb17f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
68-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\5a660b02a26ababb3b15cc631f7fb17f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
69            </intent-filter>
70        </receiver>
71    </application>
72
73</manifest>
