package com.augment.crazybrain;

import android.os.Bundle;
import android.widget.Button;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

public class MainActivity extends AppCompatActivity {
    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        Button btnStory = findViewById(R.id.btnStory);
        Button btnBoss = findViewById(R.id.btnBoss);
        Button btnPVP = findViewById(R.id.btnPVP);
        Button btnDaily = findViewById(R.id.btnDaily);
        Button btnShop = findViewById(R.id.btnShop);
        Button btnAchieve = findViewById(R.id.btnAchieve);
        Button btnPet = findViewById(R.id.btnPet);
        Button btnSettings = findViewById(R.id.btnSettings);

        // 暂时仅做占位点击
        Button[] all = new Button[]{btnStory, btnBoss, btnPVP, btnDaily, btnShop, btnAchieve, btnPet, btnSettings};
        for (Button b : all) {
            if (b != null) {
                b.setOnClickListener(v -> {
                    // TODO: 跳转到对应 Activity 或 Fragment
                });
            }
        }
    }
}

