<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="疯狂大脑"
            android:textSize="28sp"
            android:textStyle="bold"
            android:layout_gravity="center_horizontal"
            android:padding="8dp"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="融合专注力训练与奇幻塔防的创新体验"
            android:layout_gravity="center_horizontal"
            android:textSize="14sp"
            android:paddingBottom="16dp"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center_horizontal"
            android:dividerPadding="8dp">

            <Button android:id="@+id/btnStory" android:layout_width="match_parent" android:layout_height="wrap_content" android:text="剧情闯关" />
            <Button android:id="@+id/btnBoss" android:layout_width="match_parent" android:layout_height="wrap_content" android:text="Boss挑战" />
            <Button android:id="@+id/btnPVP" android:layout_width="match_parent" android:layout_height="wrap_content" android:text="实时对战" />
            <Button android:id="@+id/btnDaily" android:layout_width="match_parent" android:layout_height="wrap_content" android:text="每日训练" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingTop="16dp"
                android:weightSum="4">
                <Button android:id="@+id/btnShop" android:layout_width="0dp" android:layout_weight="1" android:layout_height="wrap_content" android:text="商店" />
                <Button android:id="@+id/btnAchieve" android:layout_width="0dp" android:layout_weight="1" android:layout_height="wrap_content" android:text="成就" />
                <Button android:id="@+id/btnPet" android:layout_width="0dp" android:layout_weight="1" android:layout_height="wrap_content" android:text="宠物" />
                <Button android:id="@+id/btnSettings" android:layout_width="0dp" android:layout_weight="1" android:layout_height="wrap_content" android:text="设置" />
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>
</ScrollView>

