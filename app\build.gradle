plugins {
    id 'com.android.application' version '8.1.4'
}

android {
    // Keep namespace stable with source set; applicationId is dynamic via APP_ID
    namespace 'com.augment.crazybrain'
    compileSdk Integer.valueOf(findProperty('COMPILE_SDK') ?: '34')

    defaultConfig {
        applicationId findProperty('APP_ID') ?: 'com.augment.crazybrain'
        minSdk Integer.valueOf(findProperty('MIN_SDK') ?: '24')
        targetSdk Integer.valueOf(findProperty('TARGET_SDK') ?: '34')
        versionCode 1
        versionName "1.0.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        debug {
            applicationIdSuffix ".debug"
        }
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.toVersion(findProperty('JAVA_VERSION') ?: '17')
        targetCompatibility JavaVersion.toVersion(findProperty('JAVA_VERSION') ?: '17')
    }
}

dependencies {
    implementation 'androidx.appcompat:appcompat:1.7.0'
    implementation 'com.google.android.material:material:1.12.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'

    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.2.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.6.1'
}

